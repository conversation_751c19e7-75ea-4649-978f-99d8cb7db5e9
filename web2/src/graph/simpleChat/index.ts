import { StateGraph, END, START, Annotation } from "@langchain/langgraph";
import { SystemMessage, HumanMessage } from "@langchain/core/messages";
import type { BaseMessageLike } from "@langchain/core/messages";
import { createModel } from "../designToCode/model/index";
import { createOpenaiModel } from "../designToCode/model/openai";

// 定义模型配置类型
interface ModelConfig {
  type: 'google' | 'openai';
  providerType?: 'htsc' | 'openrouter' | 'local';
}

const SimpleChatStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessageLike[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  input: Annotation<string>({
    reducer: (x, y) => y || x,
    default: () => "",
  }),
  output: Annotation<string>({
    reducer: (x, y) => y || x,
    default: () => "",
  }),
  modelConfig: Annotation<ModelConfig>({
    reducer: (x, y) => y || x,
    default: () => ({ type: 'google' }),
  }),
});

/**
 * 简单的聊天节点
 */
async function simpleChatNode(state: typeof SimpleChatStateAnnotation.State) {
  console.log("SimpleChat: 处理用户消息...", {
    messagesCount: state.messages.length,
    input: state.input
  });

  // 构建消息列表 - 使用现有的消息历史
  let messages = [...state.messages];

  // 如果没有消息历史，添加系统消息
  if (messages.length === 0) {
    messages.push(new SystemMessage("你是一个友好的AI助手。请简洁地回复用户的问题。"));
  }

  // 如果有额外的输入，添加为用户消息
  if (state.input && state.input.trim()) {
    messages.push(new HumanMessage(state.input));
  }

  console.log(`SimpleChat: 调用模型处理 ${messages.length} 条消息`);

  try {
    // 根据配置创建模型
    const modelConfig = state.modelConfig || { type: 'google' };
    let model;

    if (modelConfig.type === 'openai') {
      model = createOpenaiModel(modelConfig.providerType);
    } else {
      model = createModel('google');
    }

    console.log(`SimpleChat: 使用模型类型: ${modelConfig.type}${modelConfig.providerType ? ` (${modelConfig.providerType})` : ''}`);

    const responseMessage = await model.invoke(messages);

    console.log("SimpleChat: 模型响应成功", {
      responseLength: responseMessage.content?.toString().length || 0
    });

    return {
      messages: [responseMessage],
      output: responseMessage.content?.toString() || "",
    };
  } catch (error) {
    console.error("SimpleChat: 模型调用失败:", error);

    // 直接抛出错误，让前端处理
    throw error;
  }
}

// 构建简单聊天工作流图
const workflow = new StateGraph(SimpleChatStateAnnotation)
  .addNode("chat", simpleChatNode)
  .addEdge(START, "chat")
  .addEdge("chat", END);

// 编译工作流
export const graph = workflow.compile();
graph.name = "simpleChat";

console.log("SimpleChat: 图已创建并编译完成");
